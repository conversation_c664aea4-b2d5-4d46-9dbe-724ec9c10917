package role_login

import (
	"chaossrv/internal/robot"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gatePB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gate"
	loginPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/login"
	"github.com/sirupsen/logrus"
)

func InitLogin(rb *robot.Robot) {
	// 添加空指针检查
	if rb == nil {
		logrus.Errorln("InitLogin: robot is nil")
		return
	}

	// 登录态
	c := rb.Client
	if c == nil {
		logrus.Errorf("InitLogin: robot client is nil for device:%s", rb.DeviceCode)
		return
	}

	s := &LoginRoleScript{
		rb,
	}
	c.HandlerCMD(commonPB.MsgID_CMD_LOGIN_RSP, &loginPB.LoginRsp{}, s.<PERSON><PERSON><PERSON>)
	c.<PERSON><PERSON>(commonPB.MsgID_CMD_DELETE_ACCOUNT_RSP, &loginPB.DeleteAccountRsp{}, s.DeleteAccountHandler)
	c.<PERSON>(commonPB.MsgID_GATE_ANOTHER_LOGIN_NTF, &gatePB.GateAnotherLoginNtf{}, s.AnotherLoginNtfHandler)
}
