package scripts

import (
	automated "chaossrv/internal"
	"chaossrv/internal/config"
	"chaossrv/internal/robot"
	conn "chaossrv/internal/robot/socket"
	"chaossrv/mocktables"
	"chaossrv/scripts/role_cmm"
	"chaossrv/scripts/role_gm"
	"chaossrv/scripts/role_hall"
	"chaossrv/scripts/role_login"
	"chaossrv/scripts/role_msg"
	"chaossrv/scripts/role_rank"
	"chaossrv/scripts/role_spot"
	"chaossrv/scripts/role_task"
	"chaossrv/scripts/role_world"
	"errors"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gatePB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gate"
	"github.com/golang/protobuf/proto"
	"github.com/robfig/cron"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type RoleScript struct {
	TargetRobot    *robot.Robot
	ClientNtfCh    chan automated.StateHandle
	OrderID        int64
	IsRunning      bool
	IsLoginSendYet bool
	Cron           *cron.Cron
}

func (s *RoleScript) Init(rb *robot.Robot) error {
	s.Cron = cron.New()
	s.Cron.AddFunc("@every 1s", s.Update)
	s.Cron.Start()
	return nil
}

// connect 发起连接
func (s *RoleScript) connect(rb *robot.Robot) error {
	// 获取连接地址
	// var pickFirstUrl string
	// if viper.IsSet("st_tcp") {
	// 	pickFirstUrl = viper.GetString("st_tcp")
	// } else {
	// 	appUpdateReq := mocktables.AppUpdate()
	// 	logrus.Infof("[Chaos]AppUpdate回包：%v", appUpdateReq)
	// 	pickFirstUrl = appUpdateReq.InfoAddress.GetSrvUri()[0]
	// 	viper.Set("st_tcp", pickFirstUrl)
	// }

	client := conn.NewSocket(rb.DeviceCode, rb.ClientNtfCh)
	if client == nil {
		return errors.New("创建Socket失败：" + rb.DeviceCode)
	}

	// 绑定connect
	rb.Client = client
	return nil
}

func (s *RoleScript) Login(rb *robot.Robot) error {
	logHttp := logrus.WithFields(logrus.Fields{
		"No":          s.TargetRobot.SerialNo,
		"DeviceCode:": s.TargetRobot.DeviceCode,
	})

	err := s.connect(rb)
	if err != nil {
		logHttp.Errorf("[%s]NewClient失败", err.Error())
		return err
	}

	// 放在登录成功，有了Active的Client连接后,再挂载Role，这里后面可以动态添加
	role_login.InitLogin(rb)

	// 延迟只需
	time.AfterFunc(config.TimeLoginDelay, func() {
		logHttp.Infof("login send")
		rb.Report.LoginTimeS = time.Now().UnixMilli()
		loginResBody := mocktables.Login()
		loginResBody.DeviceInfo.DeviceCode = rb.DeviceCode

		// 添加空指针检查
		if rb.Client != nil {
			rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_LOGIN_REQ), loginResBody)
		} else {
			logHttp.Errorf("Client is nil when trying to send login message for device:%s", rb.DeviceCode)
		}
	})

	// 初始化消息处理器
	s.InitMsgHandler(rb)

	// 删除账号
	// time.AfterFunc(config.TimeLoginDelay, func() {
	// 	delReq := mocktables.DeleteAccount(1)
	// 	rb.Client.SendPbMsg(int(commonPB.MsgID_CMD_DELETE_ACCOUNT_REQ), delReq)
	// })

	return nil
}

func (s *RoleScript) Play(rb *robot.Robot) error {
	// time.AfterFunc(time.Second*30, func() {
	// 	s.SpotPlay(rb)
	// })

	// 压测接口 放开指定的函数测试
	// s.LoopPlay(rb)

	// 大厅
	// s.HallPlay(rb)
	// 钓点服务器
	// s.SpotPlay(rb)

	// s.WorldPlay(rb)

	// s.TaskPlay(rb)

	// s.MsgPlay(rb)

	// s.GmPlay(rb)

	// s.RankPlayer(rb)

	// 心跳
	logrus.Infof("机器人 %d 登录成功，开始执行压测逻辑", rb.UID)
	s.Cron.AddFunc("@every 5s", func() {
		s.sendHeartBeat(rb)
	})

	return nil
}

// sendHeartBeat 手动发送心跳包保持连接
func (s *RoleScript) sendHeartBeat(rb *robot.Robot) {
	if rb.Client == nil || !rb.Client.IsConn() {
		logrus.Warnf("机器人 %d 连接无效，无法发送心跳包", rb.UID)
		return
	}

	// 发送心跳包
	heartBeatReq := &gatePB.GateHeartBeatReq{
		TimeStamp: proto.Uint64(uint64(time.Now().Unix())),
	}

	rb.Client.SendPbMsg(int(commonPB.MsgID_GATE_HEART_BEAT_REQ), heartBeatReq)
	// logrus.Debugf("机器人 %d 发送心跳包，时间戳: %d", rb.UID, time.Now().Unix())
}

func (s *RoleScript) LoopPlay(rb *robot.Robot) {
	// 挂载hall协议
	role_hall.InitHall(rb)
	// 钓点服初始化
	role_spot.InitSpot(rb)

	// 全局代理
	role_cmm.InitGlobal(rb)

	// role_loop.LoopHallReq(rb)
	logrus.Debugf("robot:%d, enter hall done", rb.UID)

	// time.AfterFunc(10*time.Second, func() {
	// 	// 挂载spot协议
	// 	role_spot.InitSpot(rb)
	// 	// 钓点服请求
	// 	role_loop.LoopHookFish(rb)
	// })
}

func (s *RoleScript) InitMsgHandler(rb *robot.Robot) {
	role_msg.InitMsg(rb)
	role_gm.InitGm(rb)
	role_hall.InitHall(rb)
	role_spot.InitSpot(rb)
	role_task.InitTask(rb)
	role_world.InitWorld(rb)
	role_rank.InitRank(rb)
	role_msg.InitMsg(rb)
	role_cmm.InitGlobal(rb)
}

func (s *RoleScript) GmPlay(rb *robot.Robot) {
	role_gm.InitGm(rb)

	// role_gm.GmPlay(rb)
	// role_gm.GmSendMail(rb)
}

func (s *RoleScript) HallPlay(rb *robot.Robot) {
	// 挂载hall协议
	role_hall.InitHall(rb)

	// 全局代理
	role_cmm.InitGlobal(rb)

	// 大厅服请求
	role_hall.HallPlay(rb)
}

func (s *RoleScript) SpotPlay(rb *robot.Robot) {
	// 挂载Hall协议
	role_spot.InitSpot(rb)
	// 钓点服请求
	role_spot.SpotPlay(rb)
}

func (s *RoleScript) WorldPlay(rb *robot.Robot) {
	// 挂载Hall协议
	role_world.InitWorld(rb)

	// 钓点服请求
	role_world.WorldPlay(rb)
}

func (s *RoleScript) TaskPlay(rb *robot.Robot) {
	// 挂载Hall协议
	role_task.InitTask(rb)

	// 钓点服请求
	role_task.TaskPlay(rb)
}

func (s *RoleScript) MsgPlay(rb *robot.Robot) {
	role_msg.InitMsg(rb)

	role_msg.MsgPlay(rb)
}

func (s *RoleScript) RankPlayer(rb *robot.Robot) {
	role_rank.InitRank(rb)

	role_rank.RankPlay(rb)
}

func (s *RoleScript) Update() {
	// 这里可以改变一些参数模拟动态随机执行效果
	// robot := s.TargetRobot
	// if !s.TargetRobot.Client.IsConn {
	// 	for {
	// 		// 全部弹出
	// 		mock := robot.PopMock()
	// 		if mock == nil {
	// 			return
	// 		}
	// 		// 发送mock信息
	// 		logrus.Debugf("mock send:%d data:%+v", mock.MsgId, mock.Data)
	// 		robot.Client.SendPbMsg(mock.MsgId, mock.Data)
	// 	}
	// }

	// 临时使用简单的状态判断处理
	// TODO: 套一层行为树进行目标行动决策
	rb := s.TargetRobot
	if rb.State == robot.ROBOT_STATE_RUNNING && !rb.Client.IsConn() {
		rb.State = robot.ROBOT_STATE_DISCONNECT
	}

	switch rb.State {
	case robot.ROBOT_STATE_INIT:
		if rb.Client == nil {
			s.Login(rb)
		}

		// 发起连接/登录
	case robot.ROBOT_STATE_RUNNING:
	case robot.ROBOT_STATE_DISCONNECT:
		// 重置状态重连
		if viper.GetBool("reconnect") {
			s.reset()
			if rb.Client == nil {
				s.Login(rb)
			}
		}
	case robot.ROBOT_STATE_SHUTDOWN:
		// 关闭连接
		s.Shutdown()
	case robot.ROBOT_STATE_ANOTHER:
		// 关闭连接
		s.Another()
	}
}

func (s *RoleScript) reset() {
	rb := s.TargetRobot
	rb.State = robot.ROBOT_STATE_INIT
	rb.Client.Close()
	rb.Client = nil
	rb.UID = 0
	rb.LoginRsp = nil
}

// 暂停机器人
func (s *RoleScript) StopYet() {
	s.IsRunning = false
	s.Cron.Stop()

}

func (s *RoleScript) Shutdown() {
	s.IsRunning = false
	s.Cron.Stop()

	ret := automated.StateHandle{}
	ret.Code = automated.ConstCsLogOut
	ret.DeviceID = s.TargetRobot.DeviceCode
	ret.UID = s.TargetRobot.UID

	s.TargetRobot.ClientNtfCh <- ret

	// 主动下线 (临时处理)
	s.TargetRobot.Client.Close()
	s.TargetRobot.ClientNtfCh = nil
}

func (s *RoleScript) Another() {
	s.IsRunning = false
	s.Cron.Stop()

	// 主动下线 (临时处理)
	s.TargetRobot.Client.Close()
	s.TargetRobot.ClientNtfCh = nil
}
