package socket

import (
	automated "chaossrv/internal"
	"encoding/binary"
	"fmt"
	"net/url"
	"reflect"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gatePB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gate"

	"git.keepfancy.xyz/back-end/frameworks/kit/network/wpb"

	"github.com/golang/protobuf/proto"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// WriteMsg 写消息结构体
type WriteMsg struct {
	msgId int
	msg   proto.Message
}

type WsClient struct {
	conn *websocket.Conn
	// 设备号
	deviceCode string
	// 玩家id
	uid    uint64
	cmdMap map[int32]*clientProc // 一个玩家在同一个协程，所以逻辑上是安全的

	isConn bool // socket连接状态

	stopFlag       bool
	heatBeatCh     chan int
	unreceivedBeat int64 // 未收到心跳次数
	ClientNtfCh    chan automated.StateHandle
	QuitChan       chan interface{}

	// 写消息通道，用于序列化所有写操作
	writeCh chan WriteMsg
}

func NewWsClient(playerDeviceCode string, ClientNtfCh chan automated.StateHandle) *WsClient {
	client := &WsClient{
		deviceCode:  playerDeviceCode,
		heatBeatCh:  make(chan int),
		ClientNtfCh: ClientNtfCh,
		cmdMap:      make(map[int32]*clientProc),
		QuitChan:    make(chan interface{}),
		writeCh:     make(chan WriteMsg, 100), // 缓冲通道，避免阻塞
	}

	addr := viper.GetString("st_gateway")
	u := url.URL{Scheme: "ws", Host: addr, Path: "/ws"}
	header := make(map[string][]string)
	header["Sec-WebSocket-Protocol"] = []string{"network"}

	conn, _, err := websocket.DefaultDialer.Dial(u.String(), header)
	if err != nil {
		logrus.Errorln("Dial ERROR", err)
		return nil
	}
	client.conn = conn
	if client.conn == nil {
		logrus.Errorln("Dial conn ERROR", err)
		return nil
	}
	// 拨号成功，开始收发信息
	client.isConn = true
	go client.heartBeat()
	go client.recvLoop()
	go client.writeLoop() // 启动写消息处理goroutine

	return client
}

func (c *WsClient) BindUid(uid uint64) {
	c.uid = uid
}

func (c *WsClient) recvLoop() {
	for {
		if c.isConn {
			_, data, err := c.conn.ReadMessage()
			if err != nil {
				logrus.WithError(err).Errorln("读取数据错误")
				c.stopFlag = true
				break
			}
			// 确定连接与数据有效
			if data != nil {
				header, length := c.parseHeader(data)
				if header == nil {
					// 无数据时休眠10毫秒
					time.Sleep(100 * time.Millisecond)
					continue
				}
				c.processRsp(int(*header.MsgId), length)

				time.Sleep(20 * time.Millisecond)

				if !c.stopFlag {
					continue
				}
			}
		}

		// 通知心跳线程退出
		c.heatBeatCh <- 1
		logrus.Infof("recvLoop thread quit uid:%d deviceCode:%s", c.uid, c.deviceCode)

		// 主动关闭的不用发
		if c.isConn {
			// 通知机器人管理线程，该玩家退出
			ret := automated.StateHandle{}
			ret.Code = automated.ConstCsSocketFail
			ret.DeviceID = c.deviceCode
			ret.UID = c.uid
			c.ClientNtfCh <- ret

			c.Close()
		}
		break
	}
}

func (c *WsClient) Close() {
	c.isConn = false
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}
	// 关闭写通道，通知writeLoop退出
	close(c.writeCh)
}

// writeLoop 专门处理写操作的goroutine，确保写操作的串行化
func (c *WsClient) writeLoop() {
	for {
		select {
		case writeMsg, ok := <-c.writeCh:
			if !ok {
				// 通道已关闭，退出
				logrus.Debugf("writeLoop quit for device:%s", c.deviceCode)
				return
			}

			// 检查连接状态
			if !c.isConn {
				continue
			}

			// 执行实际的写操作
			c.doWriteMessage(writeMsg.msgId, writeMsg.msg)

		case <-c.QuitChan:
			logrus.Debugf("writeLoop quit via QuitChan for device:%s", c.deviceCode)
			return
		}
	}
}

func (c *WsClient) parseHeader(data []byte) (*wpb.Header, []byte) {
	if len(data) == 0 {
		return nil, nil
	}
	// TODO 为啥需要去掉 2字节
	data = data[2:]
	headSize := int(data[0])
	if headSize > len(data)-1 {
		logrus.Errorf("头部长度(%v)比实际长度(%v)长，容易内存越界", headSize, len(data)-1)
		return nil, nil
	}
	header := new(wpb.Header)
	err := proto.Unmarshal(data[1:1+headSize], header)
	if err != nil {
		logrus.WithError(err).Errorln("解析报文头失败")
		return nil, nil
	}
	left := len(data) - 1 - headSize
	if int(header.GetBodyLength()) != left {
		logrus.Errorf("报文头标记长度(%v)与实际报文体长度(%v)不符", header.GetBodyLength(), left)
		return nil, nil
	}
	body := data[1+headSize:]
	return header, body
}

func (c *WsClient) HandlerCMD(iCmd interface{}, rsp interface{}, handle clientHandler) error {
	// 添加空指针检查
	if c == nil {
		return fmt.Errorf("WsClient is nil")
	}

	cmd := int32(reflect.ValueOf(iCmd).Int())
	if _, ok := c.cmdMap[cmd]; ok {
		return fmt.Errorf("cmd:%d already registered", cmd)
	}
	reqType := reflect.TypeOf(rsp).Elem()
	funcName := GetFuncName(reflect.ValueOf(handle))
	cHandler := &clientProc{
		proc:     handle,
		rsp:      reqType,
		procName: funcName,
	}
	c.cmdMap[cmd] = cHandler
	return nil
}

func (c *WsClient) heartBeat() {
	hb := time.NewTicker(beatHeartInterval)
	defer hb.Stop()
	for {
		select {
		case <-hb.C:
			if c.unreceivedBeat >= beatBreakTimes {
				c.stopFlag = true
				return
			} else {
				// 心跳
				c.SendPbMsg(int(commonPB.MsgID_GATE_HEART_BEAT_REQ), &gatePB.GateHeartBeatReq{
					TimeStamp: proto.Uint64(uint64(time.Now().Unix())),
				})
				c.unreceivedBeat++
			}
		case <-c.heatBeatCh:
			logrus.Debugf("heartBeat quit")
			return
		case <-c.QuitChan:
			logrus.Debugf("quitChan quit")
			return
		}
	}
}

func (c *WsClient) SendPbMsg(msgId int, msg proto.Message) {
	// 添加空指针检查
	if c == nil {
		logrus.Errorln("SendPbMsg: WsClient is nil")
		return
	}

	if !c.isConn {
		return
	}

	// 通过channel发送消息，由writeLoop处理实际的写操作
	select {
	case c.writeCh <- WriteMsg{msgId: msgId, msg: msg}:
		// 消息已发送到通道
	default:
		// 通道满了，丢弃消息并记录日志
		logrus.Warnf("write channel full, dropping message msgId:%d for device:%s", msgId, c.deviceCode)
	}
}

// doWriteMessage 执行实际的写操作
func (c *WsClient) doWriteMessage(msgId int, msg proto.Message) {
	entry := logrus.WithFields(logrus.Fields{
		"msgID": msgId,
		"msg":   msg.String(),
	})

	body, _ := proto.Marshal(msg)
	header, _ := proto.Marshal(&wpb.Header{
		MsgId:      proto.Uint32(uint32(msgId)),
		BodyLength: proto.Uint32(uint32(len(body))),
	})

	sendData := make([]byte, 1+len(header)+len(body))
	sendData[0] = byte(len(header))
	copy(sendData[1:len(header)+1], header)
	copy(sendData[len(header)+1:], body)

	dataSize := len(sendData) + 2
	dataHeader := make([]byte, 2)
	binary.BigEndian.PutUint16(dataHeader, uint16(dataSize))

	wholeData := make([]byte, dataSize)
	copy(wholeData[:2], dataHeader)
	copy(wholeData[2:], sendData)

	err := c.conn.WriteMessage(websocket.BinaryMessage, wholeData)
	if err != nil {
		entry.WithError(err).Errorln("数据没有完整发送")
	}
}

func (c *WsClient) processRsp(msgId int, data []byte) {
	c.unreceivedBeat = 0 // 收到任何包都reset
	switch msgId {
	// 收到心跳包判断
	case int(commonPB.MsgID_GATE_HEART_BEAT_RSP):
		return
	}

	if len(data) == 0 {
		logrus.Errorf("[chaos] %d recv data empty msgId:%d", c.uid, msgId)
	}

	// 解析对应协议处理
	if handler, ok := c.cmdMap[int32(msgId)]; ok {
		pv := reflect.New(handler.rsp)
		rsp := pv.Interface().(IProto)
		err := proto.Unmarshal(data, rsp)
		if err != nil {
			return
		}
		if errProc := handler.proc(rsp); errProc != nil {
			logrus.Errorf("[chaos]handler msg error %v", errProc)
		}
	} else {
		logrus.Warnf("[chaos] %d handler msg unknown cmd : %v", c.uid, msgId)
	}
}

func (c *WsClient) IsConn() bool {
	if c == nil {
		return false
	}
	return c.isConn
}
