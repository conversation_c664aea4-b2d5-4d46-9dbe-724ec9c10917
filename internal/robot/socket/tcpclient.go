package socket

import (
	automated "chaossrv/internal"
	"encoding/binary"
	"fmt"
	"io"
	"net"
	"reflect"
	"strings"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gatePB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gate"

	"git.keepfancy.xyz/back-end/frameworks/kit/network/wpb"

	"github.com/spf13/viper"

	"github.com/golang/protobuf/proto"
	"github.com/sirupsen/logrus"
)

const beatHeartInterval = 10 * time.Second
const beatBreakTimes = 2 // 心跳未收到次数，判断断开

type clientProc struct {
	proc     func(iRsp IProto) (err error)
	procName string
	rsp      reflect.Type // rsp的反射类型
}

type TcpClient struct {
	conn net.Conn
	// 设备号
	deviceCode string
	// 玩家id
	uid    uint64
	cmdMap map[int32]*clientProc // 一个玩家在同一个协程，所以逻辑上是安全的

	isConn bool // socket连接状态

	stopFlag       bool
	heatBeatCh     chan int
	unreceivedBeat int64 // 未收到心跳次数
	ClientNtfCh    chan automated.StateHandle
	QuitChan       chan interface{}
}

func NewTcpClient(playerDeviceCode string, ClientNtfCh chan automated.StateHandle) *TcpClient {
	client := &TcpClient{
		deviceCode:  playerDeviceCode,
		heatBeatCh:  make(chan int),
		ClientNtfCh: ClientNtfCh,
		cmdMap:      make(map[int32]*clientProc),
		QuitChan:    make(chan interface{}),
	}

	addr := viper.GetString("st_tcp")
	conn, err := net.DialTimeout("tcp", addr, 10*time.Second)
	if err != nil {
		logrus.Errorln("Dial ERROR", err)
	}
	client.conn = conn
	if client.conn == nil {
		logrus.Errorln("Dial conn ERROR", err)
		return nil
	}
	// 拨号成功，开始收发信息
	client.isConn = true
	go client.heartBeat()
	go client.recvLoop()

	return client
}

func (t *TcpClient) BindUid(uid uint64) {
	t.uid = uid
}

func (t *TcpClient) recvLoop() {
	for {
		if t.isConn {
			data := t.recvData()
			// 确定连接与数据有效
			if data != nil {
				header, length := t.parseHeader(data)
				if header == nil {
					// 无数据时休眠10毫秒
					time.Sleep(100 * time.Millisecond)
					continue
				}
				t.processRsp(int(*header.MsgId), length)

				time.Sleep(20 * time.Millisecond)

				if !t.stopFlag {
					continue
				}
			}
		}

		// 通知心跳线程退出
		t.heatBeatCh <- 1
		logrus.Infof("recvLoop thread quit uid:%d deviceCode:%s", t.uid, t.deviceCode)

		// TODO: 通知上层退出/重连

		// 主动关闭的不用发
		if t.isConn {
			// 通知机器人管理线程，该玩家退出
			ret := automated.StateHandle{}
			ret.Code = automated.ConstCsSocketFail
			ret.DeviceID = t.deviceCode
			ret.UID = t.uid
			t.ClientNtfCh <- ret

			t.Close()

		}
		break
	}
}

// Close 关闭连接
func (t *TcpClient) Close() {
	t.isConn = false
	if t.conn != nil {
		t.conn.Close()
		t.conn = nil
	}
}

func (t *TcpClient) parseHeader(data []byte) (*wpb.Header, []byte) {
	if len(data) == 0 {
		return nil, nil
	}
	headSize := int(data[0])
	if headSize > len(data)-1 {
		logrus.Errorf("头部长度(%v)比实际长度(%v)长，容易内存越界", headSize, len(data)-1)
		return nil, nil
	}
	header := new(wpb.Header)
	err := proto.Unmarshal(data[1:1+headSize], header)
	if err != nil {
		logrus.WithError(err).Errorln("解析报文头失败")
		return nil, nil
	}
	left := len(data) - 1 - headSize
	if int(header.GetBodyLength()) != left {
		logrus.Errorf("报文头标记长度(%v)与实际报文体长度(%v)不符", header.GetBodyLength(), left)
		return nil, nil
	}
	body := data[1+headSize:]
	return header, body
}

func (t *TcpClient) recvData() []byte {
	byteSz := make([]byte, 2)
	_, err := io.ReadFull(t.conn, byteSz)
	if err != nil {
		if err != io.EOF {
			logrus.WithError(err).Errorln("读取长度数据错误")
		}
		return nil
	}
	sz := (int(byteSz[0]) << 8) | int(byteSz[1])
	data := make([]byte, sz-2)
	if _, err := io.ReadFull(t.conn, data); err != nil {
		logrus.WithError(err).Errorln("读取数据错误")
		return nil
	}
	return data
}

// HandlerCMD 只要设置回包命令， 回包结构，回包处理函数
func (t *TcpClient) HandlerCMD(iCmd interface{}, rsp interface{}, handle clientHandler) error {
	// 添加空指针检查
	if t == nil {
		return fmt.Errorf("TcpClient is nil")
	}

	cmd := int32(reflect.ValueOf(iCmd).Int())
	if _, ok := t.cmdMap[cmd]; ok {
		return fmt.Errorf("cmd:%d already registered", cmd)
	}
	reqType := reflect.TypeOf(rsp).Elem()
	funcName := GetFuncName(reflect.ValueOf(handle))
	cHandler := &clientProc{
		proc:     handle,
		rsp:      reqType,
		procName: funcName,
	}
	t.cmdMap[cmd] = cHandler
	return nil
}

// 连续两个间隔心跳发送没有回包则说明玩家断开连接
func (t *TcpClient) heartBeat() {
	hb := time.NewTicker(beatHeartInterval)
	defer hb.Stop()
	for {
		select {
		case <-hb.C:
			if t.unreceivedBeat >= beatBreakTimes {
				// t.IsConn = false
				t.stopFlag = true
				return
			} else {
				// 心跳
				t.SendPbMsg(int(commonPB.MsgID_GATE_HEART_BEAT_REQ), &gatePB.GateHeartBeatReq{
					TimeStamp: proto.Uint64(uint64(time.Now().Unix())),
				})
				t.unreceivedBeat++
			}
		case <-t.heatBeatCh:
			logrus.Debugf("heartBeat quit")
			return
		case <-t.QuitChan:
			logrus.Debugf("quitChan quit")
			return
		}
	}
}

func (t *TcpClient) SendPbMsg(msgId int, msg proto.Message) {
	// 添加空指针检查
	if t == nil {
		logrus.Errorln("SendPbMsg: TcpClient is nil")
		return
	}

	entry := logrus.WithFields(logrus.Fields{
		"msgID": msgId,
		"msg":   msg.String(),
	})

	if !t.isConn {
		return
	}

	body, _ := proto.Marshal(msg)
	header, _ := proto.Marshal(&wpb.Header{
		MsgId:      proto.Uint32(uint32(msgId)),
		BodyLength: proto.Uint32(uint32(len(body))),
	})

	sendData := make([]byte, 1+len(header)+len(body))
	sendData[0] = byte(len(header))
	copy(sendData[1:len(header)+1], header)
	copy(sendData[len(header)+1:], body)

	dataSize := len(sendData) + 2
	dataHeader := make([]byte, 2)
	binary.BigEndian.PutUint16(dataHeader, uint16(dataSize))

	wholeData := make([]byte, dataSize)
	copy(wholeData[:2], dataHeader)
	copy(wholeData[2:], sendData)
	n, err := t.conn.Write(wholeData)
	if err != nil || n != dataSize {
		// 检查是否是连接断开相关的错误
		if err != nil && t.isConnectionError(err) {
			logrus.Warnf("Connection broken for device:%s, marking as disconnected. Error: %v", t.deviceCode, err)
			t.isConn = false
			t.stopFlag = true
		} else {
			entry.WithError(err).WithField("expect", dataSize).WithField("actual", n).Errorln("数据没有完整发送")
		}
	}
}

// 处理响应包
func (t *TcpClient) processRsp(msgId int, data []byte) {

	t.unreceivedBeat = 0 // 收到任何包都reset
	switch msgId {
	// 收到心跳包判断
	case int(commonPB.MsgID_GATE_HEART_BEAT_RSP):
		// logrus.Infof("[chaos] recv beat heart : %v", t.uid)
		return
	}

	if len(data) == 0 {
		logrus.Errorf("[chaos] %d recv data empty msgId:%d", t.uid, msgId)
	}

	// 解析对应协议处理
	if c, ok := t.cmdMap[int32(msgId)]; ok {
		pv := reflect.New(c.rsp)
		rsp := pv.Interface().(IProto)
		err := proto.Unmarshal(data, rsp)
		if err != nil {
			return
		}
		if errProc := c.proc(rsp); errProc != nil {
			logrus.Errorf("[chaos]handler msg error %v", errProc)
		}
	} else {
		logrus.Warnf("[chaos] %d handler msg unknown cmd : %v", t.uid, msgId)
	}
}

func (t *TcpClient) IsConn() bool {
	if t == nil {
		return false
	}
	return t.isConn
}

// isConnectionError 判断是否是连接断开相关的错误
func (t *TcpClient) isConnectionError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	// 检查常见的连接断开错误
	connectionErrors := []string{
		"broken pipe",
		"connection reset by peer",
		"connection refused",
		"use of closed network connection",
		"EOF",
	}

	for _, connErr := range connectionErrors {
		if strings.Contains(errStr, connErr) {
			return true
		}
	}

	return false
}
