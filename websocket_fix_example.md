# WebSocket 并发写入问题修复

## 问题描述

原始错误：
```
panic: concurrent write to websocket connection

goroutine 67527 [running]:
github.com/gorilla/websocket.(*messageWriter).flushFrame(0x140219fc990, 0x1, {0x0, 0x0, 0x0})
	/Users/<USER>/go/pkg/mod/github.com/gorilla/websocket@v1.5.1/conn.go:632 +0x7b4
github.com/gorilla/websocket.(*messageWriter).Close(0x140219fc990)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/websocket@v1.5.1/conn.go:746 +0x70
github.com/gorilla/websocket.(*Conn).beginMessage(0x140095fe420, 0x14021bd4300, 0x2)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/websocket@v1.5.1/conn.go:493 +0x60
github.com/gorilla/websocket.(*Conn).NextWriter(0x140095fe420, 0x2)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/websocket@v1.5.1/conn.go:535 +0x88
github.com/gorilla/websocket.(*Conn).WriteMessage(0x140095fe420, 0x2, {0x140214977a0, 0xe, 0xe})
	/Users/<USER>/go/pkg/mod/github.com/gorilla/websocket@v1.5.1/conn.go:788 +0x23c
chaossrv/internal/robot/socket.(*WsClient).SendPbMsg(0x1400e2dbea0, 0x3e9, {0x105868eb0, 0x14021bd4270})
	/Users/<USER>/go/src/fancygame/chaossrv/internal/robot/socket/wsclient.go:226 +0x790
chaossrv/internal/robot/socket.(*WsClient).heartBeat(0x1400e2dbea0)
	/Users/<USER>/go/src/fancygame/chaossrv/internal/robot/socket/wsclient.go:182 +0x2b0
```

## 问题原因

WebSocket 连接不支持并发写入。在原始代码中，多个 goroutine 同时调用 `SendPbMsg` 方法：

1. **心跳 goroutine** - `heartBeat()` 方法定期发送心跳包
2. **业务逻辑 goroutine** - 各种游戏逻辑脚本调用 `SendPbMsg` 发送消息

这导致了对同一个 WebSocket 连接的并发写入，触发 panic。

## 解决方案

使用 **channel** 来序列化所有写操作，这是 Go 语言处理并发的惯用方式。

### 核心设计

1. **写消息结构体**：
```go
type WriteMsg struct {
    msgId int
    msg   proto.Message
}
```

2. **添加写消息通道**：
```go
type WsClient struct {
    // ... 其他字段
    writeCh chan WriteMsg  // 写消息通道，用于序列化所有写操作
}
```

3. **专用写操作 goroutine**：
```go
func (c *WsClient) writeLoop() {
    for {
        select {
        case writeMsg, ok := <-c.writeCh:
            if !ok {
                return // 通道已关闭，退出
            }
            if c.isConn {
                c.doWriteMessage(writeMsg.msgId, writeMsg.msg)
            }
        case <-c.QuitChan:
            return
        }
    }
}
```

4. **修改 SendPbMsg 方法**：
```go
func (c *WsClient) SendPbMsg(msgId int, msg proto.Message) {
    if !c.isConn {
        return
    }
    
    // 通过channel发送消息，由writeLoop处理实际的写操作
    select {
    case c.writeCh <- WriteMsg{msgId: msgId, msg: msg}:
        // 消息已发送到通道
    default:
        // 通道满了，丢弃消息并记录日志
        logrus.Warnf("write channel full, dropping message msgId:%d for device:%s", msgId, c.deviceCode)
    }
}
```

## 优势

1. **并发安全**：所有写操作都在单一 goroutine 中执行，避免并发写入
2. **非阻塞**：使用带缓冲的 channel 和 select default，避免阻塞调用方
3. **Go 惯用法**：使用 channel 而不是 mutex，符合 "Don't communicate by sharing memory; share memory by communicating" 的理念
4. **优雅关闭**：通过关闭 channel 来通知 writeLoop 退出
5. **背压处理**：当写通道满时，丢弃消息并记录日志，防止内存泄漏

## 测试建议

运行压力测试验证修复效果：
```bash
# 启动压测
go run cmd/main.go

# 观察日志，确认不再出现 "concurrent write to websocket connection" 错误
tail -f logs/chaos/chaos_*.log | grep -i "concurrent\|panic"
```

## 注意事项

1. **缓冲区大小**：writeCh 使用了 100 的缓冲区，可根据实际需求调整
2. **消息丢失**：当写通道满时会丢弃消息，这是为了防止内存泄漏的权衡
3. **错误处理**：doWriteMessage 中的错误会被记录但不会影响其他消息的发送
